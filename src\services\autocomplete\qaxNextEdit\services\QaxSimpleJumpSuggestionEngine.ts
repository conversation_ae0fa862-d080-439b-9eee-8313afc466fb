import * as vscode from 'vscode'
import { QaxLSPService } from './QaxLSPService'
import { 
    QaxAnalysisContext, 
    QaxChangeDetection, 
    QaxChangeType,
    QaxJumpSuggestion,
    QaxNextEditConfig 
} from '../types/QaxNextEditTypes'

/**
 * 简化的跳转建议引擎
 * 专注于基于LSP引用的建议生成，移除复杂的逻辑
 */
export class QaxSimpleJumpSuggestionEngine {
    private lspService: QaxLSPService
    private config: QaxNextEditConfig

    constructor(config: QaxNextEditConfig) {
        this.config = config
        this.lspService = QaxLSPService.getInstance()
    }

    /**
     * 生成跳转建议 - 简化版本
     */
    async generateJumpSuggestions(changes: QaxChangeDetection[], context: QaxAnalysisContext): Promise<QaxJumpSuggestion[]> {
        const suggestions: QaxJumpSuggestion[] = []

        // 只处理变量重命名，这是最常见的用例
        for (const change of changes) {
            if (change.type === QaxChangeType.VARIABLE_RENAME && change.oldValue && change.newValue) {
                const renameSuggestions = await this.generateRenameSuggestions(change, context)
                suggestions.push(...renameSuggestions)
            }
        }

        // 按优先级排序并限制数量
        return suggestions
            .sort((a, b) => b.priority - a.priority)
            .slice(0, this.config.maxSuggestions || 10)
    }

    /**
     * 生成重命名建议
     */
    private async generateRenameSuggestions(
        change: QaxChangeDetection, 
        context: QaxAnalysisContext
    ): Promise<QaxJumpSuggestion[]> {
        const suggestions: QaxJumpSuggestion[] = []

        try {
            // 获取原符号的引用
            const references = await this.lspService.getReferencesFromOriginalContent(
                context.beforeContent,
                context.filePath,
                change.range.start,
                context.languageId
            )

            // 为每个引用生成建议
            for (const reference of references) {
                // 跳过当前修改位置
                if (this.isCurrentChangeLocation(reference, change)) {
                    continue
                }

                suggestions.push({
                    id: `rename-${change.oldValue}-${reference.uri.fsPath}-${reference.range.start.line}`,
                    filePath: reference.uri.fsPath,
                    range: reference.range,
                    description: `Update '${change.oldValue}' to '${change.newValue}'`,
                    changeType: change.type,
                    suggestedEdit: {
                        range: reference.range,
                        newText: change.newValue || '',
                        description: `Replace ${change.oldValue} with ${change.newValue}`
                    },
                    priority: change.confidence,
                    relatedChange: change
                })
            }

        } catch (error) {
            // 静默处理错误，不影响其他建议的生成
        }

        return suggestions
    }

    /**
     * 检查是否是当前修改位置
     */
    private isCurrentChangeLocation(reference: vscode.Location, change: QaxChangeDetection): boolean {
        return reference.range.start.line === change.range.start.line &&
               reference.range.start.character === change.range.start.character
    }
}
