# QaxSimpleChangeDetector 修复验证

## 问题描述
从日志中发现的问题：
1. `Initial content length: 0` - beforeContent为空
2. 虽然检测到了diff (`showAddEventModal` -> `showAddNewEventModal`)，但没有找到引用

## 根本原因
在 `QaxNextEditService.ts` 第173行：
```typescript
const beforeContent = this.documentContentCache.get(filePath) || ""
```

当文档第一次被编辑时，`documentContentCache` 中没有该文件的缓存，所以 `beforeContent` 被设置为空字符串。

## 修复方案
1. **初始化文档缓存**: 在服务启动时缓存所有已打开的文档
2. **跳过首次编辑**: 当没有缓存内容时，缓存当前内容但跳过本次分析

## 修复内容

### 1. 添加初始化方法
```typescript
private initializeDocumentCache(): void {
    // 缓存所有可见编辑器中的文档
    vscode.window.visibleTextEditors.forEach((editor) => {
        if (this.shouldAnalyzeDocument(editor.document)) {
            this.cacheDocumentContent(editor.document)
        }
    })
    
    // 缓存当前活动文档
    if (vscode.window.activeTextEditor) {
        const activeDoc = vscode.window.activeTextEditor.document
        if (this.shouldAnalyzeDocument(activeDoc)) {
            this.cacheDocumentContent(activeDoc)
        }
    }
}
```

### 2. 修改文档变更处理逻辑
```typescript
// 获取变更前的内容
let beforeContent = this.documentContentCache.get(filePath)
const afterContent = event.document.getText()

if (!beforeContent) {
    // 第一次编辑该文档，缓存当前内容供下次使用，但跳过本次分析
    console.log(`🔍 QaxNextEdit: First edit for ${filePath}, caching content for future analysis`)
    this.cacheDocumentContent(event.document)
    return
}
```

## 预期效果
1. 服务启动时会缓存所有已打开的文档
2. 第一次编辑时会跳过分析，但缓存内容
3. 后续编辑时会有正确的 beforeContent，能够：
   - 正确检测符号变更
   - 使用LSP服务找到引用
   - 生成正确的跳转建议

## 测试验证
修复后的行为应该是：
1. 打开一个包含函数的JavaScript文件
2. 重命名函数名（如 `showAddEventModal` -> `showAddNewEventModal`）
3. 应该能看到：
   - 检测到1个变更
   - 找到多个引用位置
   - 生成相应的跳转建议

## 单元测试更新
更新了 `QaxSimpleChangeDetector.test.ts`：
1. 修复了类型错误（添加缺失的配置属性和rangeOffset）
2. 添加了更全面的测试用例
3. 包括了LSP集成测试和错误处理测试
