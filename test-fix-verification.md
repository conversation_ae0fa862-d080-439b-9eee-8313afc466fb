# QaxSimpleChangeDetector 修复验证

## 问题描述
从日志中发现的问题：
1. `Initial content length: 0` - beforeContent为空
2. 虽然检测到了diff (`showAddEventModal` -> `showAddNewEventModal`)，但没有找到引用

## 根本原因
真正的问题出现在编辑会话跟踪中：

1. **文档缓存问题**: 当文档第一次被编辑时，`documentContentCache` 中没有该文件的缓存，所以 `beforeContent` 为空字符串
2. **编辑会话初始化问题**: 在 `trackEditSession` 方法中，`session.initialContent` 被设置为空的 `beforeContent`
3. **会话结束分析问题**: 在 `endEditSession` 中，使用 `session.initialContent` 作为分析的 `beforeContent`，导致 `Initial content length: 0`

## 修复方案
1. **初始化文档缓存**: 在服务启动时缓存所有已打开的文档
2. **修复编辑会话初始化**: 当 `beforeContent` 为空时，使用 `afterContent` 作为会话的初始内容
3. **改进日志记录**: 添加更详细的日志来帮助调试

## 修复内容

### 1. 添加初始化方法
```typescript
private initializeDocumentCache(): void {
    // 缓存所有可见编辑器中的文档
    vscode.window.visibleTextEditors.forEach((editor) => {
        if (this.shouldAnalyzeDocument(editor.document)) {
            this.cacheDocumentContent(editor.document)
        }
    })
    
    // 缓存当前活动文档
    if (vscode.window.activeTextEditor) {
        const activeDoc = vscode.window.activeTextEditor.document
        if (this.shouldAnalyzeDocument(activeDoc)) {
            this.cacheDocumentContent(activeDoc)
        }
    }
}
```

### 2. 修复编辑会话初始化
```typescript
if (!session) {
    // 开始新的编辑会话
    // 如果beforeContent为空，使用afterContent作为初始内容（这是第一次编辑的情况）
    const initialContent = beforeContent || afterContent
    session = {
        startTime: now,
        lastEditTime: now,
        initialContent: initialContent,
        editCount: 1,
        isActive: true
    }
    this.editSessions.set(filePath, session)
    console.log(`📝 QaxNextEdit: Started edit session for ${filePath}, initial content length: ${initialContent.length}`)
}
```

### 3. 改进文档变更处理
```typescript
// 获取变更前的内容
let beforeContent = this.documentContentCache.get(filePath)
const afterContent = event.document.getText()

// 如果没有缓存的beforeContent，这可能是第一次编辑
// 我们仍然继续分析，但会在编辑会话跟踪中处理这种情况
if (!beforeContent) {
    console.log(`🔍 QaxNextEdit: No cached content for ${filePath}, this might be the first edit`)
    beforeContent = "" // 明确设置为空字符串
}
```

## 预期效果
1. 服务启动时会缓存所有已打开的文档
2. 第一次编辑时：
   - 编辑会话会使用当前文档内容作为初始内容
   - 不会出现 `Initial content length: 0` 的情况
   - 能够正确进行符号变更检测
3. 后续编辑时会有正确的 beforeContent，能够：
   - 正确检测符号变更
   - 使用LSP服务找到引用
   - 生成正确的跳转建议

## 测试验证
修复后的行为应该是：
1. 打开一个包含函数的JavaScript文件
2. 重命名函数名（如 `showAddEventModal` -> `showAddNewEventModal`）
3. 应该能看到：
   - 检测到1个变更
   - 找到多个引用位置
   - 生成相应的跳转建议

## 单元测试更新
更新了 `QaxSimpleChangeDetector.test.ts`：
1. 修复了类型错误（添加缺失的配置属性和rangeOffset）
2. 添加了更全面的测试用例
3. 包括了LSP集成测试和错误处理测试
