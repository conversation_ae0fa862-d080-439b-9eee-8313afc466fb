// 简单的测试脚本来验证QaxSimpleChangeDetector的修复
const { QaxSimpleChangeDetector } = require('./src/services/autocomplete/qaxNextEdit/services/QaxSimpleChangeDetector');

// 模拟VSCode API
const mockVscode = {
    Range: function(start, end) { return { start, end }; },
    Position: function(line, character) { return { line, character }; },
    Location: function(uri, range) { return { uri, range }; },
    Uri: {
        file: function(path) { return { fsPath: path }; }
    }
};

// 模拟LSP服务
const mockLSPService = {
    getReferencesFromOriginalContent: async function(originalContent, filePath, position, languageId) {
        console.log(`🔍 Mock LSP: Getting references for content length: ${originalContent.length}`);
        console.log(`🔍 Mock LSP: Position: ${position.line}:${position.character}`);
        
        // 模拟找到的引用
        if (originalContent.includes('showAddEventModal')) {
            return [
                new mockVscode.Location(
                    mockVscode.Uri.file(filePath),
                    new mockVscode.Range(new mockVscode.Position(4, 0), new mockVscode.Position(4, 17))
                ),
                new mockVscode.Location(
                    mockVscode.Uri.file(filePath),
                    new mockVscode.Range(new mockVscode.Position(5, 5), new mockVscode.Position(5, 22))
                ),
                new mockVscode.Location(
                    mockVscode.Uri.file(filePath),
                    new mockVscode.Range(new mockVscode.Position(6, 4), new mockVscode.Position(6, 21))
                ),
            ];
        }
        return [];
    },
    isLSPAvailable: async function() { return true; }
};

// 模拟配置
const mockConfig = {
    enabled: true,
    enableLSPIntegration: true,
    enableASTAnalysis: false,
    confidenceThreshold: 0.5,
    debounceDelayMs: 300,
    maxSuggestions: 10,
    enableEditSessionTracking: false,
    minEditSessionDurationMs: 1000,
    supportedLanguages: ['javascript', 'typescript'],
    analysisDepth: 'shallow',
    editSessionTimeoutMs: 5000,
};

async function testSimpleChangeDetector() {
    console.log('🧪 Testing QaxSimpleChangeDetector...');
    
    try {
        // 创建检测器实例
        const detector = new QaxSimpleChangeDetector(mockConfig);
        
        // 注入mock LSP服务
        detector.lspService = mockLSPService;
        
        // 模拟文档
        const mockDocument = {
            offsetAt: function(pos) {
                // 简单的offset计算
                return pos.line * 100 + pos.character;
            }
        };
        
        // 测试用例：函数重命名
        const beforeContent = `showAddEventModal(date) {
    // function body
}

showAddEventModal();
this.showAddEventModal(date);
obj.showAddEventModal();`;

        const afterContent = `showAddNewEventModal(date) {
    // function body
}

showAddEventModal();
this.showAddEventModal(date);
obj.showAddEventModal();`;

        const context = {
            filePath: '/test/calendar.js',
            document: mockDocument,
            changes: [{
                range: new mockVscode.Range(new mockVscode.Position(0, 0), new mockVscode.Position(0, 17)),
                rangeLength: 17,
                rangeOffset: 0,
                text: 'showAddNewEventModal',
            }],
            beforeContent,
            afterContent,
            languageId: 'javascript',
        };

        console.log('📝 Testing with context:');
        console.log(`  - beforeContent length: ${beforeContent.length}`);
        console.log(`  - afterContent length: ${afterContent.length}`);
        console.log(`  - changes: ${context.changes.length}`);

        const result = await detector.analyzeChanges(context);

        console.log('✅ Analysis result:');
        console.log(`  - detectedChanges: ${result.detectedChanges.length}`);
        console.log(`  - jumpSuggestions: ${result.jumpSuggestions.length}`);
        console.log(`  - confidence: ${result.confidence}`);
        console.log(`  - analysisTime: ${result.analysisTime}ms`);

        if (result.detectedChanges.length > 0) {
            console.log('🔍 Detected changes:');
            result.detectedChanges.forEach((change, i) => {
                console.log(`  ${i + 1}. ${change.oldValue} -> ${change.newValue} (${change.type})`);
            });
        }

        if (result.jumpSuggestions.length > 0) {
            console.log('💡 Jump suggestions:');
            result.jumpSuggestions.forEach((suggestion, i) => {
                console.log(`  ${i + 1}. ${suggestion.description}`);
                console.log(`     File: ${suggestion.filePath}`);
                console.log(`     Range: ${suggestion.range.start.line}:${suggestion.range.start.character}`);
            });
        }

        // 验证结果
        if (result.detectedChanges.length === 1 && result.jumpSuggestions.length === 3) {
            console.log('🎉 Test PASSED: Correctly detected function rename and found references!');
            return true;
        } else {
            console.log('❌ Test FAILED: Expected 1 change and 3 suggestions');
            return false;
        }

    } catch (error) {
        console.error('❌ Test FAILED with error:', error);
        return false;
    }
}

// 运行测试
testSimpleChangeDetector().then(success => {
    process.exit(success ? 0 : 1);
});
