import * as vscode from 'vscode'
import { QaxSimpleChangeDetector } from '../QaxSimpleChangeDetector'
import { QaxLSPService } from '../QaxLSPService'
import { QaxAnalysisContext, QaxChangeType, QaxNextEditConfig } from '../../types/QaxNextEditTypes'

// Mock VSCode API
jest.mock('vscode', () => ({
    workspace: {
        openTextDocument: jest.fn(),
    },
    Range: jest.fn().mockImplementation((start, end) => ({ start, end })),
    Position: jest.fn().mockImplementation((line, character) => ({ line, character })),
    Location: jest.fn().mockImplementation((uri, range) => ({ uri, range })),
    Uri: {
        file: jest.fn().mockImplementation((path) => ({ fsPath: path })),
    },
}))

// Mock QaxLSPService
jest.mock('../QaxLSPService')

describe('QaxSimpleChangeDetector', () => {
    let detector: QaxSimpleChangeDetector
    let mockLSPService: jest.Mocked<QaxLSPService>
    let mockConfig: QaxNextEditConfig

    beforeEach(() => {
        mockConfig = {
            enabled: true,
            enableLSPIntegration: true,
            enableASTAnalysis: false,
            confidenceThreshold: 0.5,
            debounceDelayMs: 300,
            maxSuggestions: 10,
            enableEditSessionTracking: false,
            minEditSessionDurationMs: 1000,
            supportedLanguages: ['javascript', 'typescript'],
            analysisDepth: 'shallow',
            editSessionTimeoutMs: 5000,
        }

        mockLSPService = {
            getInstance: jest.fn().mockReturnThis(),
            isLSPAvailable: jest.fn().mockResolvedValue(true),
            getReferencesFromOriginalContent: jest.fn().mockResolvedValue([]),
        } as any

        ;(QaxLSPService.getInstance as jest.Mock).mockReturnValue(mockLSPService)

        detector = new QaxSimpleChangeDetector(mockConfig)
    })

    afterEach(() => {
        jest.clearAllMocks()
    })

    describe('analyzeChanges', () => {
        it('should return empty result for invalid context', async () => {
            const result = await detector.analyzeChanges(null as any)

            expect(result.detectedChanges).toEqual([])
            expect(result.jumpSuggestions).toEqual([])
            expect(result.confidence).toBe(0)
            expect(result.metadata?.skippedReason).toBe('invalid_context')
        })

        it('should return empty result when content is unchanged', async () => {
            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: {} as vscode.TextDocument,
                changes: [],
                beforeContent: 'const test = 1;',
                afterContent: 'const test = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toEqual([])
            expect(result.jumpSuggestions).toEqual([])
            expect(result.metadata?.skippedReason).toBe('no_changes')
        })

        it('should detect variable rename', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    rangeOffset: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;',
                afterContent: 'const newVar = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(1)
            expect(result.detectedChanges[0].type).toBe(QaxChangeType.VARIABLE_RENAME)
            expect(result.detectedChanges[0].oldValue).toBe('oldVar')
            expect(result.detectedChanges[0].newValue).toBe('newVar')
        })

        it('should generate suggestions when LSP is enabled', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const mockReferences = [
                new vscode.Location(
                    vscode.Uri.file('/test/file.ts'),
                    new vscode.Range(new vscode.Position(1, 0), new vscode.Position(1, 6))
                ),
                new vscode.Location(
                    vscode.Uri.file('/test/file.ts'),
                    new vscode.Range(new vscode.Position(2, 12), new vscode.Position(2, 18))
                ),
            ]

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences)

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    rangeOffset: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;\noldVar.toString();\nconsole.log(oldVar);',
                afterContent: 'const newVar = 1;\noldVar.toString();\nconsole.log(oldVar);',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(1)
            expect(result.jumpSuggestions).toHaveLength(2) // 2 references excluding the definition
            expect(result.jumpSuggestions[0].description).toContain('Update \'oldVar\' to \'newVar\'')
        })

        it('should handle function rename with multiple references correctly', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => {
                    // 模拟真实的offsetAt行为
                    const lines = 'showAddEventModal(date) {\n    // function body\n}\n\nshowAddEventModal();\nthis.showAddEventModal(date);\nobj.showAddEventModal();'.split('\n')
                    let offset = 0
                    for (let i = 0; i < pos.line && i < lines.length; i++) {
                        offset += lines[i].length + 1 // +1 for newline
                    }
                    return offset + pos.character
                }),
            } as any

            // 模拟找到的引用位置
            const mockReferences = [
                // 函数定义位置
                new vscode.Location(
                    vscode.Uri.file('/test/calendar.js'),
                    new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, 17))
                ),
                // 第一个调用
                new vscode.Location(
                    vscode.Uri.file('/test/calendar.js'),
                    new vscode.Range(new vscode.Position(4, 0), new vscode.Position(4, 17))
                ),
                // 第二个调用
                new vscode.Location(
                    vscode.Uri.file('/test/calendar.js'),
                    new vscode.Range(new vscode.Position(5, 5), new vscode.Position(5, 22))
                ),
                // 第三个调用
                new vscode.Location(
                    vscode.Uri.file('/test/calendar.js'),
                    new vscode.Range(new vscode.Position(6, 4), new vscode.Position(6, 21))
                ),
            ]

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences)

            const beforeContent = `showAddEventModal(date) {
    // function body
}

showAddEventModal();
this.showAddEventModal(date);
obj.showAddEventModal();`

            const afterContent = `showAddNewEventModal(date) {
    // function body
}

showAddEventModal();
this.showAddEventModal(date);
obj.showAddEventModal();`

            const context: QaxAnalysisContext = {
                filePath: '/test/calendar.js',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, 17)),
                    rangeLength: 17,
                    rangeOffset: 0,
                    text: 'showAddNewEventModal',
                }],
                beforeContent,
                afterContent,
                languageId: 'javascript',
            }

            const result = await detector.analyzeChanges(context)

            // 验证检测到的变更
            expect(result.detectedChanges).toHaveLength(1)
            expect(result.detectedChanges[0].type).toBe(QaxChangeType.VARIABLE_RENAME)
            expect(result.detectedChanges[0].oldValue).toBe('showAddEventModal')
            expect(result.detectedChanges[0].newValue).toBe('showAddNewEventModal')

            // 验证生成的建议 - 应该包含所有引用位置，除了当前修改位置
            expect(result.jumpSuggestions).toHaveLength(3) // 4个引用 - 1个当前修改位置 = 3个建议

            // 验证建议的内容
            expect(result.jumpSuggestions[0].description).toContain('Update \'showAddEventModal\' to \'showAddNewEventModal\'')
            expect(result.jumpSuggestions[0].suggestedEdit?.newText).toBe('showAddNewEventModal')

            // 验证LSP服务被正确调用
            expect(mockLSPService.getReferencesFromOriginalContent).toHaveBeenCalledWith(
                beforeContent,
                '/test/calendar.js',
                new vscode.Position(0, 0),
                'javascript'
            )
        })

        it('should handle empty beforeContent gracefully', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, 6)),
                    rangeLength: 6,
                    rangeOffset: 0,
                    text: 'newVar',
                }],
                beforeContent: '', // 空的beforeContent
                afterContent: 'newVar',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toEqual([])
            expect(result.jumpSuggestions).toEqual([])
            expect(result.metadata?.skippedReason).toBe('invalid_context')
        })

        it('should skip non-identifier changes', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 8), new vscode.Position(0, 9)),
                    rangeLength: 1,
                    rangeOffset: 8,
                    text: '2',
                }],
                beforeContent: 'const x = 1;',
                afterContent: 'const x = 2;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(0)
            expect(result.metadata?.skippedReason).toBe('no_symbol_changes')
        })

        it('should not generate suggestions when LSP is disabled', async () => {
            const configWithoutLSP = { ...mockConfig, enableLSPIntegration: false }
            detector = new QaxSimpleChangeDetector(configWithoutLSP)

            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    rangeOffset: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;',
                afterContent: 'const newVar = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(1)
            expect(result.jumpSuggestions).toHaveLength(0) // No suggestions without LSP
        })
    })

    describe('LSP integration', () => {
        it('should call LSP service with correct parameters', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const beforeContent = `function showAddEventModal(date) {
    console.log('Adding event for', date);
}

// Usage
showAddEventModal(new Date());
this.showAddEventModal(today);`

            const afterContent = `function showAddNewEventModal(date) {
    console.log('Adding event for', date);
}

// Usage
showAddEventModal(new Date());
this.showAddEventModal(today);`

            const context: QaxAnalysisContext = {
                filePath: '/test/calendar.js',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 9), new vscode.Position(0, 26)),
                    rangeLength: 17,
                    rangeOffset: 9,
                    text: 'showAddNewEventModal',
                }],
                beforeContent,
                afterContent,
                languageId: 'javascript',
            }

            // 设置LSP服务返回一些引用
            const mockReferences = [
                new vscode.Location(
                    vscode.Uri.file('/test/calendar.js'),
                    new vscode.Range(new vscode.Position(5, 0), new vscode.Position(5, 17))
                ),
                new vscode.Location(
                    vscode.Uri.file('/test/calendar.js'),
                    new vscode.Range(new vscode.Position(6, 5), new vscode.Position(6, 22))
                ),
            ]
            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences)

            await detector.analyzeChanges(context)

            // 验证LSP服务被调用时使用了正确的参数
            expect(mockLSPService.getReferencesFromOriginalContent).toHaveBeenCalledWith(
                beforeContent, // 应该传递完整的原始内容
                '/test/calendar.js',
                new vscode.Position(0, 9), // 变更的起始位置
                'javascript'
            )
        })

        it('should handle LSP service errors gracefully', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            // 模拟LSP服务抛出错误
            mockLSPService.getReferencesFromOriginalContent.mockRejectedValue(new Error('LSP service error'))

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    rangeOffset: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;',
                afterContent: 'const newVar = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            // 应该检测到变更，但没有建议（因为LSP失败）
            expect(result.detectedChanges).toHaveLength(1)
            expect(result.jumpSuggestions).toHaveLength(0)
        })
    })

    describe('identifier validation', () => {
        it('should identify valid identifiers', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const validCases = [
                { old: 'myVar', new: 'newVar' },
                { old: '_private', new: 'public' },
                { old: '$jquery', new: 'vanilla' },
                { old: 'Component1', new: 'Component2' },
            ]

            for (const testCase of validCases) {
                const context: QaxAnalysisContext = {
                    filePath: '/test/file.ts',
                    document: mockDocument,
                    changes: [{
                        range: new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, testCase.old.length)),
                        rangeLength: testCase.old.length,
                        rangeOffset: 0,
                        text: testCase.new,
                    }],
                    beforeContent: testCase.old,
                    afterContent: testCase.new,
                    languageId: 'typescript',
                }

                const result = await detector.analyzeChanges(context)
                expect(result.detectedChanges).toHaveLength(1)
            }
        })

        it('should reject invalid identifiers', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const invalidCases = [
                { old: '123', new: '456' },
                { old: '"string"', new: '"other"' },
                { old: '++', new: '--' },
                { old: 'a-b', new: 'c-d' },
            ]

            for (const testCase of invalidCases) {
                const context: QaxAnalysisContext = {
                    filePath: '/test/file.ts',
                    document: mockDocument,
                    changes: [{
                        range: new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, testCase.old.length)),
                        rangeLength: testCase.old.length,
                        rangeOffset: 0,
                        text: testCase.new,
                    }],
                    beforeContent: testCase.old,
                    afterContent: testCase.new,
                    languageId: 'typescript',
                }

                const result = await detector.analyzeChanges(context)
                expect(result.detectedChanges).toHaveLength(0)
            }
        })
    })
})
