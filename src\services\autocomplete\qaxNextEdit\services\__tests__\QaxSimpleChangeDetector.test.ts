import * as vscode from 'vscode'
import { QaxSimpleChangeDetector } from '../QaxSimpleChangeDetector'
import { QaxLSPService } from '../QaxLSPService'
import { QaxAnalysisContext, QaxChangeType, QaxNextEditConfig } from '../../types/QaxNextEditTypes'

// Mock VSCode API
jest.mock('vscode', () => ({
    workspace: {
        openTextDocument: jest.fn(),
    },
    Range: jest.fn().mockImplementation((start, end) => ({ start, end })),
    Position: jest.fn().mockImplementation((line, character) => ({ line, character })),
    Location: jest.fn().mockImplementation((uri, range) => ({ uri, range })),
    Uri: {
        file: jest.fn().mockImplementation((path) => ({ fsPath: path })),
    },
}))

// Mock QaxLSPService
jest.mock('../QaxLSPService')

describe('QaxSimpleChangeDetector', () => {
    let detector: QaxSimpleChangeDetector
    let mockLSPService: jest.Mocked<QaxLSPService>
    let mockConfig: QaxNextEditConfig

    beforeEach(() => {
        mockConfig = {
            enabled: true,
            enableLSPIntegration: true,
            enableASTAnalysis: false,
            confidenceThreshold: 0.5,
            debounceDelayMs: 300,
            maxSuggestions: 10,
            enableEditSessionTracking: false,
            minEditSessionDurationMs: 1000,
        }

        mockLSPService = {
            getInstance: jest.fn().mockReturnThis(),
            isLSPAvailable: jest.fn().mockResolvedValue(true),
            getReferencesFromOriginalContent: jest.fn().mockResolvedValue([]),
        } as any

        ;(QaxLSPService.getInstance as jest.Mock).mockReturnValue(mockLSPService)

        detector = new QaxSimpleChangeDetector(mockConfig)
    })

    afterEach(() => {
        jest.clearAllMocks()
    })

    describe('analyzeChanges', () => {
        it('should return empty result for invalid context', async () => {
            const result = await detector.analyzeChanges(null as any)

            expect(result.detectedChanges).toEqual([])
            expect(result.jumpSuggestions).toEqual([])
            expect(result.confidence).toBe(0)
            expect(result.metadata.skippedReason).toBe('invalid_context')
        })

        it('should return empty result when content is unchanged', async () => {
            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: {} as vscode.TextDocument,
                changes: [],
                beforeContent: 'const test = 1;',
                afterContent: 'const test = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toEqual([])
            expect(result.jumpSuggestions).toEqual([])
            expect(result.metadata.skippedReason).toBe('no_changes')
        })

        it('should detect variable rename', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;',
                afterContent: 'const newVar = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(1)
            expect(result.detectedChanges[0].type).toBe(QaxChangeType.VARIABLE_RENAME)
            expect(result.detectedChanges[0].oldValue).toBe('oldVar')
            expect(result.detectedChanges[0].newValue).toBe('newVar')
        })

        it('should generate suggestions when LSP is enabled', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const mockReferences = [
                new vscode.Location(
                    vscode.Uri.file('/test/file.ts'),
                    new vscode.Range(new vscode.Position(1, 0), new vscode.Position(1, 6))
                ),
                new vscode.Location(
                    vscode.Uri.file('/test/file.ts'),
                    new vscode.Range(new vscode.Position(2, 12), new vscode.Position(2, 18))
                ),
            ]

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences)

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;\noldVar.toString();\nconsole.log(oldVar);',
                afterContent: 'const newVar = 1;\noldVar.toString();\nconsole.log(oldVar);',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(1)
            expect(result.jumpSuggestions).toHaveLength(2) // 2 references excluding the definition
            expect(result.jumpSuggestions[0].description).toContain('Update \'oldVar\' to \'newVar\'')
        })

        it('should skip non-identifier changes', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 8), new vscode.Position(0, 9)),
                    rangeLength: 1,
                    text: '2',
                }],
                beforeContent: 'const x = 1;',
                afterContent: 'const x = 2;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(0)
            expect(result.metadata.skippedReason).toBe('no_symbol_changes')
        })

        it('should not generate suggestions when LSP is disabled', async () => {
            const configWithoutLSP = { ...mockConfig, enableLSPIntegration: false }
            detector = new QaxSimpleChangeDetector(configWithoutLSP)

            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const context: QaxAnalysisContext = {
                filePath: '/test/file.ts',
                document: mockDocument,
                changes: [{
                    range: new vscode.Range(new vscode.Position(0, 6), new vscode.Position(0, 12)),
                    rangeLength: 6,
                    text: 'newVar',
                }],
                beforeContent: 'const oldVar = 1;',
                afterContent: 'const newVar = 1;',
                languageId: 'typescript',
            }

            const result = await detector.analyzeChanges(context)

            expect(result.detectedChanges).toHaveLength(1)
            expect(result.jumpSuggestions).toHaveLength(0) // No suggestions without LSP
        })
    })

    describe('identifier validation', () => {
        it('should identify valid identifiers', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const validCases = [
                { old: 'myVar', new: 'newVar' },
                { old: '_private', new: 'public' },
                { old: '$jquery', new: 'vanilla' },
                { old: 'Component1', new: 'Component2' },
            ]

            for (const testCase of validCases) {
                const context: QaxAnalysisContext = {
                    filePath: '/test/file.ts',
                    document: mockDocument,
                    changes: [{
                        range: new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, testCase.old.length)),
                        rangeLength: testCase.old.length,
                        text: testCase.new,
                    }],
                    beforeContent: testCase.old,
                    afterContent: testCase.new,
                    languageId: 'typescript',
                }

                const result = await detector.analyzeChanges(context)
                expect(result.detectedChanges).toHaveLength(1)
            }
        })

        it('should reject invalid identifiers', async () => {
            const mockDocument = {
                offsetAt: jest.fn().mockImplementation((pos) => pos.line * 100 + pos.character),
            } as any

            const invalidCases = [
                { old: '123', new: '456' },
                { old: '"string"', new: '"other"' },
                { old: '++', new: '--' },
                { old: 'a-b', new: 'c-d' },
            ]

            for (const testCase of invalidCases) {
                const context: QaxAnalysisContext = {
                    filePath: '/test/file.ts',
                    document: mockDocument,
                    changes: [{
                        range: new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, testCase.old.length)),
                        rangeLength: testCase.old.length,
                        text: testCase.new,
                    }],
                    beforeContent: testCase.old,
                    afterContent: testCase.new,
                    languageId: 'typescript',
                }

                const result = await detector.analyzeChanges(context)
                expect(result.detectedChanges).toHaveLength(0)
            }
        })
    })
})
