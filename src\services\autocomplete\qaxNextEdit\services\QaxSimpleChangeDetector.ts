import * as vscode from 'vscode'
import { QaxLSPService } from './QaxLSPService'
import {
    QaxAnalysisContext,
    QaxAnalysisResult,
    QaxChangeDetection,
    QaxChangeType,
    QaxJumpSuggestion,
    QaxNextEditConfig
} from '../types/QaxNextEditTypes'

/**
 * 简化的变更检测器 - 只关注实际的符号修改
 * 
 * 核心思路：
 * 1. 用户修改了什么 -> 直接从变更事件获取
 * 2. 获取原符号的引用 -> 使用LSP
 * 3. 生成修改建议 -> 基于引用位置
 */
export class QaxSimpleChangeDetector {
    private lspService: QaxLSPService
    private config: QaxNextEditConfig

    constructor(config: QaxNextEditConfig) {
        this.lspService = QaxLSPService.getInstance()
        this.config = config
    }

    /**
     * 更新配置
     */
    updateConfig(config: QaxNextEditConfig): void {
        this.config = config
    }

    /**
     * 分析变更 - 简化版本
     */
    async analyzeChanges(context: QaxAnalysisContext): Promise<QaxAnalysisResult> {
        const startTime = Date.now()

        // 早期验证
        if (!context?.changes?.length || !context.beforeContent || !context.afterContent) {
            return this.createEmptyResult(startTime, 'invalid_context')
        }

        // 快速检查：如果内容相同，直接返回
        if (context.beforeContent === context.afterContent) {
            return this.createEmptyResult(startTime, 'no_changes')
        }

        // 1. 直接从变更事件中提取符号修改
        console.log(`🔍 SimpleChangeDetector: Analyzing ${context.changes.length} changes`)
        console.log(`🔍 SimpleChangeDetector: beforeContent length: ${context.beforeContent.length}`)
        console.log(`🔍 SimpleChangeDetector: afterContent length: ${context.afterContent.length}`)

        const symbolChanges = this.extractSymbolChanges(context)
        console.log(`🔍 SimpleChangeDetector: Extracted ${symbolChanges.length} symbol changes`)

        if (symbolChanges.length === 0) {
            console.log(`🔍 SimpleChangeDetector: No symbol changes detected, returning empty result`)
            return this.createEmptyResult(startTime, 'no_symbol_changes')
        }

        // 2. 为每个符号修改生成建议
        const allSuggestions: QaxJumpSuggestion[] = []

        for (const change of symbolChanges) {
            if (this.config.enableLSPIntegration) {
                const suggestions = await this.generateSuggestionsForChange(change, context)
                allSuggestions.push(...suggestions)
            }
        }

        return {
            detectedChanges: symbolChanges,
            jumpSuggestions: allSuggestions,
            analysisTime: Date.now() - startTime,
            confidence: this.calculateConfidence(symbolChanges),
            metadata: {
                lspAvailable: await this.lspService.isLSPAvailable(context.document),
                astParsed: false,
                symbolsFound: symbolChanges.length,
                referencesFound: allSuggestions.length,
            }
        }
    }

    /**
     * 从变更事件中直接提取符号修改
     * 不需要检测所有符号，只看实际变更的地方
     */
    private extractSymbolChanges(context: QaxAnalysisContext): QaxChangeDetection[] {
        const changes: QaxChangeDetection[] = []

        for (const change of context.changes) {
            console.log(`🔍 SimpleChangeDetector: Processing change - text: "${change.text}", rangeLength: ${change.rangeLength}`)

            // 跳过空白变更
            if (!change.text.trim() && change.rangeLength === 0) {
                console.log(`🔍 SimpleChangeDetector: Skipping empty change`)
                continue
            }

            // 获取变更前的文本
            const startOffset = context.document.offsetAt(change.range.start)
            const endOffset = startOffset + change.rangeLength
            const oldText = context.beforeContent.substring(startOffset, endOffset)
            const newText = change.text

            console.log(`🔍 SimpleChangeDetector: Change details - oldText: "${oldText}", newText: "${newText}"`)
            console.log(`🔍 SimpleChangeDetector: Position - start: ${change.range.start.line}:${change.range.start.character}, offset: ${startOffset}-${endOffset}`)

            // 只处理看起来像标识符的变更
            if (this.looksLikeIdentifierChange(oldText, newText)) {
                console.log(`🔍 SimpleChangeDetector: Detected identifier change: "${oldText}" -> "${newText}"`)
                changes.push({
                    type: this.determineChangeType(oldText, newText),
                    filePath: context.filePath,
                    range: change.range,
                    oldValue: oldText,
                    newValue: newText,
                    confidence: this.calculateChangeConfidence(oldText, newText),
                    metadata: {
                        detectionMethod: 'direct_change_analysis'
                    }
                })
            } else {
                console.log(`🔍 SimpleChangeDetector: Not an identifier change, skipping`)
            }
        }

        return changes
    }

    /**
     * 为单个符号变更生成建议
     */
    private async generateSuggestionsForChange(
        change: QaxChangeDetection, 
        context: QaxAnalysisContext
    ): Promise<QaxJumpSuggestion[]> {
        const suggestions: QaxJumpSuggestion[] = []

        try {
            // 获取原符号的引用
            const references = await this.lspService.getReferencesFromOriginalContent(
                context.beforeContent,
                context.filePath,
                change.range.start,
                context.languageId
            )

            console.log(`🔍 SimpleChangeDetector: Found ${references.length} references for ${change.oldValue}`)

            // 为每个引用生成建议
            for (const reference of references) {
                // 跳过当前修改位置
                if (this.isCurrentChangeLocation(reference, change)) {
                    continue
                }

                suggestions.push({
                    id: `${change.type}-${reference.uri.fsPath}-${reference.range.start.line}-${reference.range.start.character}`,
                    filePath: reference.uri.fsPath,
                    range: reference.range,
                    description: `Update '${change.oldValue}' to '${change.newValue}' at line ${reference.range.start.line + 1}`,
                    changeType: change.type,
                    suggestedEdit: {
                        range: reference.range,
                        newText: change.newValue || '',
                        description: `Replace ${change.oldValue} with ${change.newValue}`
                    },
                    priority: change.confidence,
                    relatedChange: change
                })
            }

        } catch (error) {
            console.warn(`🔍 SimpleChangeDetector: Failed to get references for ${change.oldValue}:`, error)
        }

        return suggestions
    }

    /**
     * 判断是否看起来像标识符变更
     */
    private looksLikeIdentifierChange(oldText: string, newText: string): boolean {
        // 两者都应该是有效的标识符
        return this.isValidIdentifier(oldText) && this.isValidIdentifier(newText)
    }

    /**
     * 判断是否是有效的标识符
     */
    private isValidIdentifier(text: string): boolean {
        if (!text || text.length === 0) return false
        
        // 基本的标识符规则：字母开头，包含字母数字下划线
        return /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(text)
    }

    /**
     * 确定变更类型
     */
    private determineChangeType(oldText: string, newText: string): QaxChangeType {
        // 简单判断：如果都是标识符，就是变量重命名
        return QaxChangeType.VARIABLE_RENAME
    }

    /**
     * 计算变更置信度
     */
    private calculateChangeConfidence(oldText: string, newText: string): number {
        // 基于文本相似度和长度
        if (oldText.length < 2 || newText.length < 2) {
            return 0.3 // 太短的变更置信度低
        }

        if (oldText.length > 3 && newText.length > 3) {
            return 0.8 // 较长的标识符置信度高
        }

        return 0.6 // 中等置信度
    }

    /**
     * 检查是否是当前修改位置
     */
    private isCurrentChangeLocation(reference: vscode.Location, change: QaxChangeDetection): boolean {
        return reference.range.start.line === change.range.start.line &&
               reference.range.start.character === change.range.start.character
    }

    /**
     * 计算整体置信度
     */
    private calculateConfidence(changes: QaxChangeDetection[]): number {
        if (changes.length === 0) return 0
        
        const avgConfidence = changes.reduce((sum, change) => sum + change.confidence, 0) / changes.length
        return avgConfidence
    }

    /**
     * 创建空结果
     */
    private createEmptyResult(startTime: number, reason?: string): QaxAnalysisResult {
        return {
            detectedChanges: [],
            jumpSuggestions: [],
            analysisTime: Date.now() - startTime,
            confidence: 0,
            metadata: {
                lspAvailable: false,
                astParsed: false,
                symbolsFound: 0,
                referencesFound: 0,
                skippedReason: reason
            }
        }
    }
}
